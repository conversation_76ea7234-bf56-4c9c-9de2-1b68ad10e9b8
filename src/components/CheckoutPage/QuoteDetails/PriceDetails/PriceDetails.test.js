import React from 'react';
import { Decimal } from 'decimal.js';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PriceDetails from './PriceDetails';
import { mocked } from 'test-utils';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import {
  getPointsAmount,
  getVoucherAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getTravelPassAmount,
  getInitialCashAmount,
  getPayWith,
} from 'store/checkout/checkoutSelectors';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import { ThemeProvider } from 'emotion-theming';
import theme from 'lib/theme';

import PaymentBreakdown from 'components/PaymentBreakdown';
import PriceBreakdownModal from 'components/CheckoutPage/PriceBreakdownModal';
import Currency from 'components/Currency';
import PriceStrikethrough from 'components/PriceStrikethrough';

jest.mock('components/PaymentBreakdown', () => {
  return jest.fn(() => null);
});

jest.mock('components/CheckoutPage/PriceBreakdownModal', () => {
  return jest.fn(() => null);
});
jest.mock('components/PriceStrikethrough', () => {
  return jest.fn(() => null);
});
jest.mock('components/Currency', () => {
  return jest.fn(({ amount, currency, 'data-testid': dataTestId }) => <div data-testid={dataTestId}>{`${currency} ${amount}`}</div>);
});

jest.mock('@qga/roo-ui/components', () => ({
  Flex: ({ children, ...props }) => (
    <div data-test="roo-ui-flex" {...props}>
      {children}
    </div>
  ),
  Box: ({ children, ...props }) => (
    <div data-test="roo-ui-box" {...props}>
      {children}
    </div>
  ),
  Text: ({ children, ...props }) => (
    <span data-test="roo-ui-text" {...props}>
      {children}
    </span>
  ),
}));

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough');

const mockStore = configureStore();

const payableAtPropertyTotal = {
  amount: new Decimal(10),
  currency: 'AUD',
};

const property = {
  name: 'Test hotel',
};

const offer = {
  type: 'standard',
  charges: {
    payableAtProperty: { total: payableAtPropertyTotal },
    payableAtBooking: { total: { pointsStrikethrough: 2000, points: 1000 } },
    strikethrough: {
      price: {
        amount: '179000',
        currency: 'AUD',
      },
    },
  },
};

const checkIn = new Date(2020, 9, 1);
const checkOut = new Date(2020, 9, 2);
const pointsAmount = new Decimal(1000);
const voucherAmount = new Decimal(40);
const travelPassAmount = new Decimal(100);
const payableNowCashAmount = new Decimal(100);
const payableLaterCashAmount = new Decimal(200);
const payableLaterDueDate = new Date(2020, 10, 10);
const initialCashAmount = new Decimal(0);

const defaultProps = {
  property,
  offer,
  checkIn,
  checkOut,
};

const renderComponent = (props = {}) => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <PriceDetails {...defaultProps} {...props} />
      </ThemeProvider>
    </Provider>,
  );
};

beforeEach(() => {
  jest.clearAllMocks();

  getPointsAmount.mockReturnValue(pointsAmount);
  getVoucherAmount.mockReturnValue(voucherAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
  getInitialCashAmount.mockReturnValue(initialCashAmount);
  getPayWith.mockReturnValue('cash');
  mocked(usePriceStrikethrough).mockReturnValue({
    isReady: true,
    isPriceStrikethrough: false,
  });
});

it('renders the PaymentBreakdown with correct props', () => {
  renderComponent();

  expect(PaymentBreakdown).toHaveBeenCalledWith(
    expect.objectContaining({
      payableNowCashAmount,
      payableLaterCashAmount,
      payableLaterDueDate,
      pointsAmount,
      travelPassAmount,
      payableAtProperty: payableAtPropertyTotal,
      priceStrikethrough: defaultProps.offer.charges.strikethrough.price,
    }),
    {},
  );
});

it('renders the PriceBreakdownModal with correct props', () => {
  renderComponent();

  expect(PriceBreakdownModal).toHaveBeenCalledWith(
    expect.objectContaining({
      property,
      offer,
      checkIn,
      checkOut,
    }),
    {},
  );
});

it('renders the voucher details', () => {
  renderComponent();

  expect(screen.getByTestId('voucher-amount')).toHaveTextContent(`AUD ${voucherAmount.negated().toString()}`);
  expect(Currency).toHaveBeenCalledWith(
    expect.objectContaining({
      amount: voucherAmount.negated(),
      currency: 'AUD',
      'data-testid': 'voucher-amount',
    }),
    {},
  );
});

describe('without a voucher', () => {
  it('does not render the voucher details', () => {
    getVoucherAmount.mockReturnValue(new Decimal(0));
    renderComponent();

    expect(screen.queryByTestId('voucher-amount')).not.toBeInTheDocument();
  });
});

describe('when in PPP mode', () => {
  beforeEach(() => {
    getPayWith.mockReturnValue('points');
    getInitialCashAmount.mockReturnValue(new Decimal(1000));
  });

  describe('and qantas-hotels-price-strikethrough feature flag OFF', () => {
    it('does NOT render the PriceStrikethrough', () => {
      renderComponent();
    });

    it('does NOT render PriceBreakdownModal specifically for PPP or general', () => {
      renderComponent();
      expect(screen.queryByTestId('price-breakdown-modal-ppp')).not.toBeInTheDocument();
      expect(screen.queryByTestId('price-breakdown-modal')).not.toBeInTheDocument();
    });
  });

  describe('and qantas-hotels-price-strikethrough feature flag ON', () => {
    beforeEach(() => {
      usePriceStrikethrough.mockReturnValue({
        isReady: true,
        isPriceStrikethrough: true,
      });
    });

    it('sends the correct props to Currency and PriceStrikethrough', () => {
      renderComponent();

      expect(PriceStrikethrough).toHaveBeenCalledWith(
        expect.objectContaining({
          price: { amount: offer.charges.payableAtBooking.total.pointsStrikethrough, currency: 'PTS' },
        }),
        {},
      );

      expect(Currency).toHaveBeenCalledWith(
        expect.objectContaining({
          amount: offer.charges.payableAtBooking.total.points,
          currency: expect.any(String),
        }),
        {},
      );
    });
  });
});
