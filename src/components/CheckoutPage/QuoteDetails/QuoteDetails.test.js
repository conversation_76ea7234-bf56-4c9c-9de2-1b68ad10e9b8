import React from 'react';
import { Decimal } from 'decimal.js';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import QuoteDetails from './QuoteDetails';
import { getPointsAmount } from 'store/checkout/checkoutSelectors';
import { getStayDates, getQuote, getOccupants } from 'store/quote/quoteSelectors';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import useGlobalInclusions from 'hooks/optimizely/useGlobalInclusions';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';

jest.mock('./PropertyDetails', () => ({ property, roomType, offer }) => (
  <div data-testid="property-details">
    <span data-testid="property-name">{property?.name}</span>
    <span data-testid="room-type-name">{roomType?.name}</span>
    <span data-testid="offer-type">{offer?.type}</span>
  </div>
));
jest.mock('./StayDates', () => ({ checkIn, checkOut }) => (
  <div data-testid="stay-dates">
    {checkIn?.toDateString()} - {checkOut?.toDateString()}
  </div>
));

jest.mock('./PriceDetails', () => ({ offer, property, checkIn, checkOut }) => (
  <div data-testid="price-details">
    <span data-testid="price-offer-type">{offer?.type}</span>
    <span data-testid="price-property-name">{property?.name}</span>
    <span data-testid="price-check-in">{checkIn?.toDateString()}</span>
    <span data-testid="price-check-out">{checkOut?.toDateString()}</span>
  </div>
));

jest.mock('components/CancellationRefundModal', () => ({
  __esModule: true,
  default: jest.fn(({ cancellationPolicy, ...props }) => (
    <div data-testid="cancellation-refund-modal" data-cancellation-policy={JSON.stringify(cancellationPolicy)} {...props}>
      Mock CancellationRefundModal
    </div>
  )),
}));

jest.mock('components/OccupantsSummary', () => ({ occupants }) => (
  <div data-testid="occupants-summary">
    Adults: {occupants?.adults}, Children: {occupants?.children}, Infants: {occupants?.infants}
  </div>
));
jest.mock('./PointsEarnSummary', () => ({ pointsEarned }) => (
  <div data-testid="points-earn-summary">
    QBR: {pointsEarned?.qbrPoints?.total}, QFF: {pointsEarned?.qffPoints?.total}
  </div>
));
jest.mock('./Inclusions', () => () => <div data-testid="inclusions-component" />);
jest.mock('components/RoomsAvailabilityMessage', () => () => <div data-testid="rooms-availability-message" />);

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('hooks/optimizely/useAvailableRoomsMessage');
jest.mock('hooks/optimizely/useGlobalInclusions');

const quote = {
  property: { name: 'name' },
  roomType: { name: 'name' },
  offer: {
    type: 'classic',
    inclusions: [
      { name: 'wi-fi', description: 'wi-fi Included' },
      { name: 'breakfast', description: 'breakfast Included' },
    ],
    cancellationPolicy: {
      isNonrefundable: true,
      description: 'sorry no refunds',
      cancellationWindows: [],
    },
    pointsEarned: {
      qbrPoints: {
        total: 3000,
      },
      qffPoints: {
        base: 5139,
        total: 5139,
      },
    },
  },
};

const noInclusionsQuote = { ...quote, offer: { ...quote.offer, inclusions: [] } };

const stayDates = { checkIn: new Date(2020, 9, 1), checkOut: new Date(2020, 9, 2) };
const occupants = { adults: 2, children: 2, infants: 2 };

beforeEach(() => {
  getQuote.mockReturnValue(quote);
  getStayDates.mockReturnValue(stayDates);
  getPointsAmount.mockReturnValue(new Decimal(10000));
  getOccupants.mockReturnValue(occupants);
  useAvailableRoomsMessage.mockReturnValue({
    isReady: true,
    showMessage: false,
    max_rooms_cutoff: 5,
  });
  useGlobalInclusions.mockReturnValue({
    isReady: true,
    isGlobalInclusions: false,
  });
});
const mockStore = configureStore([]);
const renderComponent = () => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <QuoteDetails />
    </Provider>,
  );
};

describe('QuoteDetails component', () => {
  it('renders the PropertyDetails with correct props', () => {
    renderComponent();
    expect(screen.getByTestId('property-details')).toBeInTheDocument();
    expect(screen.getByTestId('property-name')).toHaveTextContent('name');
    expect(screen.getByTestId('room-type-name')).toHaveTextContent('name');
    expect(screen.getByTestId('offer-type')).toHaveTextContent('classic');
  });

  it('renders StayDates with the correct props', () => {
    renderComponent();
    expect(screen.getByTestId('stay-dates')).toBeInTheDocument();
    expect(screen.getByTestId('stay-dates')).toHaveTextContent('Thu Oct 01 2020 - Fri Oct 02 2020');
  });

  it('renders OccupantsSummary with the correct props', () => {
    renderComponent();
    expect(screen.getByTestId('occupants-summary')).toBeInTheDocument();
    expect(screen.getByTestId('occupants-summary')).toHaveTextContent('Adults: 2, Children: 2, Infants: 2');
  });

  it('renders PointsEarnSummary with expected props', () => {
    renderComponent();
    expect(screen.getByTestId('points-earn-summary')).toBeInTheDocument();
    expect(screen.getByTestId('points-earn-summary')).toHaveTextContent('QBR: 3000, QFF: 5139');
  });

  it('displays a comma separated list of inclusions when they exist', () => {
    renderComponent();
    expect(screen.getByTestId('inclusion-descriptions')).toHaveTextContent('wi-fi Included, breakfast Included');
  });

  it('does not display a comma separated list of inclusions when they do not exist', () => {
    getQuote.mockReturnValue(noInclusionsQuote);
    renderComponent();
    expect(screen.queryByTestId('inclusion-descriptions')).not.toBeInTheDocument();
  });

  it('displays the CancellationRefundModal component with correct props', () => {
    renderComponent();

    expect(screen.getByTestId('cancellation-refund-modal')).toBeInTheDocument();
  });

  it('renders the PriceDetails with the correct props for a non-classic offer', () => {
    const notClassicQuote = { ...quote, offer: { ...quote.offer, type: 'not classic' } };
    getQuote.mockReturnValue(notClassicQuote);
    renderComponent();
    expect(screen.getByTestId('price-details')).toBeInTheDocument();
    expect(screen.getByTestId('price-offer-type')).toHaveTextContent('not classic');
    expect(screen.getByTestId('price-property-name')).toHaveTextContent('name');
    expect(screen.getByTestId('price-check-in')).toHaveTextContent('Thu Oct 01 2020');
    expect(screen.getByTestId('price-check-out')).toHaveTextContent('Fri Oct 02 2020');
  });

  it('renders nothing when no quote', () => {
    getQuote.mockReturnValue(null);
    const { container } = renderComponent();
    expect(container).toBeEmptyDOMElement();
  });

  describe('when quote offer is null', () => {
    beforeEach(() => {
      getQuote.mockReturnValue({ ...quote, offer: null });
    });

    it('does not render OccupantsSummary', () => {
      renderComponent();
      expect(screen.queryByTestId('occupants-summary')).not.toBeInTheDocument();
    });

    it('does not render StayDates', () => {
      renderComponent();
      expect(screen.queryByTestId('stay-dates')).not.toBeInTheDocument();
    });

    it('does not render PriceDetails', () => {
      renderComponent();
      expect(screen.queryByTestId('price-details')).not.toBeInTheDocument();
    });

    it('does not render CancellationRefundModal', () => {
      renderComponent();
      expect(screen.queryByTestId('cancellation-refund-modal')).not.toBeInTheDocument();
    });

    it('does not render the offer inclusions description', () => {
      renderComponent();
      expect(screen.queryByTestId('inclusion-descriptions')).not.toBeInTheDocument();
    });

    it('does not render the PointsEarnSummary', () => {
      renderComponent();
      expect(screen.queryByTestId('points-earn-summary')).not.toBeInTheDocument();
    });
  });

  describe('qantas_hotels_global_inclusions feature flag', () => {
    describe('when is OFF', () => {
      it('does not render the Inclusions component', () => {
        renderComponent();
        expect(screen.queryByTestId('inclusions-component')).not.toBeInTheDocument();
      });

      it('renders the Inclusions description', () => {
        renderComponent();
        expect(screen.getByTestId('inclusion-descriptions')).toBeInTheDocument();
      });
    });

    describe('when is ON', () => {
      beforeEach(() => {
        useGlobalInclusions.mockReturnValue({
          isReady: true,
          isGlobalInclusions: true,
        });
      });
      it('renders the Inclusions component', () => {
        renderComponent();
        expect(screen.getByTestId('inclusions-component')).toBeInTheDocument();
      });

      it('does not render the Inclusions description', () => {
        renderComponent();
        expect(screen.queryByTestId('inclusion-descriptions')).not.toBeInTheDocument();
      });
    });
  });

  describe('qantas-hotels-available-rooms-message experiment', () => {
    describe('is OFF', () => {
      it('does not render RoomsAvailabilityMessage', () => {
        renderComponent();
        expect(screen.queryByTestId('rooms-availability-message')).not.toBeInTheDocument();
      });
    });

    describe('is ON', () => {
      beforeEach(() => {
        useAvailableRoomsMessage.mockReturnValue({
          isReady: true,
          showMessage: true,
          max_rooms_cutoff: 5,
        });
      });
      it('renders RoomsAvailabilityMessage', () => {
        renderComponent();
        expect(screen.getByTestId('rooms-availability-message')).toBeInTheDocument();
      });
    });
  });
});
