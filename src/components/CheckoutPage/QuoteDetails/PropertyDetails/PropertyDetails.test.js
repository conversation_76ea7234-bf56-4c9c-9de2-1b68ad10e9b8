import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PropertyDetails from './PropertyDetails';
import { ThemeProvider } from 'emotion-theming';
import theme from 'lib/theme';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';

jest.mock('components/Image', () => {
  return jest.fn(({ src, alt }) => <img src={src} alt={alt} data-testid="mock-image" />);
});
jest.mock('components/TripAdvisorRating', () => {
  return jest.fn(({ rating }) => <div data-testid="mock-tripadvisor-rating">{JSON.stringify(rating)}</div>);
});
jest.mock('./RoomTypeDetails', () => {
  return jest.fn(({ roomType }) => <div data-testid="mock-room-type-details">{roomType?.name}</div>);
});
jest.mock('./OfferDetails', () => {
  return jest.fn(({ offer }) => <div data-testid="mock-offer-details">{offer?.name}</div>);
});
jest.mock('components/PromotionalSash', () => {
  return jest.fn(() => <div data-testid="mock-promotional-sash">Promotional Sash</div>);
});

jest.mock('@qga/roo-ui/components', () => {
  const mockStarRating = jest.fn(({ ...props }) => <div data-testid="roo-ui-star-rating" {...props} />);

  return {
    StarRating: mockStarRating,
    Box: ({ children, ...props }) => (
      <div data-testid="roo-ui-box" {...props}>
        {children}
      </div>
    ),
    Text: ({ children, ...props }) => (
      <span data-testid="roo-ui-text" {...props}>
        {children}
      </span>
    ),
    Flex: ({ children, ...props }) => (
      <div data-testid="roo-ui-flex" {...props}>
        {children}
      </div>
    ),
  };
});

const defaultProps = {
  property: {
    name: 'Grand Hotel',
    rating: 5,
    ratingType: 'SELF_RATED',
    mainImage: {
      urlMedium: 'www.imageMedium.com',
      urlLarge: 'www.imageLarge.com',
      caption: 'image description',
    },
    address: {
      streetAddress: ['Main St.'],
      suburb: 'Melbourne',
      state: 'Vic',
      country: 'Au',
    },
  },
  offer: {
    name: 'offer name',
    promotion: {
      name: 'Double Points',
    },
  },
  roomType: {
    name: 'room name',
  },
};

const mockStore = configureStore();

const renderComponent = (props = {}) => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <PropertyDetails {...defaultProps} {...props} />
      </ThemeProvider>
    </Provider>,
  );
};

describe('<PropertyDetails />', () => {
  const { property } = defaultProps;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when promotion is present', () => {
    it('returns <PromotionalSash />', () => {
      renderComponent();
      expect(screen.getByTestId('mock-promotional-sash')).toBeInTheDocument();
    });
  });

  describe('when promotion is not present', () => {
    it('does not return <PromotionalSash />', () => {
      const propsWithoutPromotion = {
        ...defaultProps,
        offer: {
          ...defaultProps.offer,
          promotion: null,
        },
      };
      renderComponent(propsWithoutPromotion);

      expect(screen.queryByTestId('mock-promotional-sash')).not.toBeInTheDocument();
    });
  });

  it('renders the property image with the correct props', () => {
    renderComponent();

    expect(screen.getByTestId('mock-image')).toHaveAttribute('alt', property.mainImage.caption);
    expect(screen.getByTestId('mock-image')).toHaveAttribute('src', property.mainImage.urlMedium);
  });

  it('renders the property name', () => {
    renderComponent();
    const propertyName = property.name;

    expect(screen.getByText(propertyName)).toBeInTheDocument();
  });

  it('renders the star ratings with the correct props', () => {
    renderComponent();
    const starRating = screen.getByTestId('roo-ui-star-rating');
    expect(starRating).toBeInTheDocument();
    expect(starRating).toHaveAttribute('rating', property.rating.toString());
    expect(starRating).toHaveAttribute('ratingtype', property.ratingType);
  });

  it('does not render star rating if rating is 0', () => {
    const propsWithZeroRating = {
      ...defaultProps,
      property: {
        ...defaultProps.property,
        rating: 0,
      },
    };

    renderComponent(propsWithZeroRating);

    expect(screen.queryByTestId('roo-ui-star-rating')).not.toBeInTheDocument();
  });

  it('hides the TripAdvisor rating when absent', () => {
    const propsWithoutTripAdvisor = {
      ...defaultProps,
      property: {
        ...defaultProps.property,
        customerRatings: [],
      },
    };
    renderComponent(propsWithoutTripAdvisor);

    expect(screen.queryByTestId('mock-tripadvisor-rating')).not.toBeInTheDocument();
  });

  it('shows the TripAdvisor rating when present', () => {
    const tripAdvisorRating = {
      tripAdvisorId: '1388984',
      source: 'trip_advisor',
      id: '14546728',
      ratingImageUrl: 'www.tripadvisor.com/img/cdsi/img2/ratings/traveler/4.5-15969-4.png',
      averageRating: 4.5,
      reviewCount: 4352,
    };
    const props = {
      ...defaultProps,
      property: {
        ...defaultProps.property,
        customerRatings: [tripAdvisorRating],
      },
    };
    renderComponent(props);

    expect(screen.getByTestId('mock-tripadvisor-rating')).toBeInTheDocument();
    expect(screen.getByTestId('mock-tripadvisor-rating')).toHaveTextContent(JSON.stringify(tripAdvisorRating));
  });

  describe('when the room and offer are present', () => {
    it('renders the RoomTypeDetails', () => {
      renderComponent();

      expect(screen.getByTestId('mock-room-type-details')).toBeInTheDocument();
      expect(screen.getByTestId('mock-room-type-details')).toHaveTextContent(defaultProps.roomType.name);
    });

    it('renders the OfferDetails', () => {
      renderComponent();

      expect(screen.getByTestId('mock-offer-details')).toBeInTheDocument();
      expect(screen.getByTestId('mock-offer-details')).toHaveTextContent(defaultProps.offer.name);
    });
  });

  describe('when the room type is not present due to an unavailable quote', () => {
    it('does not render the RoomTypeDetails', () => {
      const props = {
        ...defaultProps,
        roomType: null,
      };
      renderComponent(props);

      expect(screen.queryByTestId('mock-room-type-details')).not.toBeInTheDocument();
    });

    it('does not render the OfferDetails', () => {
      const props = {
        ...defaultProps,
        roomType: null,
      };
      renderComponent(props);

      expect(screen.queryByTestId('mock-offer-details')).not.toBeInTheDocument();
    });
  });

  describe('when the offer is not present due to an unavailable quote', () => {
    it('does not render the RoomTypeDetails', () => {
      const props = {
        ...defaultProps,
        offer: null,
      };
      renderComponent(props);

      expect(screen.queryByTestId('mock-room-type-details')).not.toBeInTheDocument();
    });

    it('does not render the OfferDetails', () => {
      const props = {
        ...defaultProps,
        offer: null,
      };
      renderComponent(props);

      expect(screen.queryByTestId('mock-offer-details')).not.toBeInTheDocument();
    });
  });
});
