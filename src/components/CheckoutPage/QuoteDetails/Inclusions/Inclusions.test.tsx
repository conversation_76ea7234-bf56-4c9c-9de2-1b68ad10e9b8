import React from 'react';
import { render, screen } from '@testing-library/react';
import Inclusions from './Inclusions';

import userEvent from '@testing-library/user-event';

jest.mock('@qga/roo-ui/components', () => ({
  Flex: ({ 'data-testid': dataTestId, alignItems, flexDirection, children, ...props }) => (
    <div style={{ alignItems, flexDirection }} data-testid={dataTestId ?? 'roo-ui-flex'} {...props}>
      {children}
    </div>
  ),
  Text: ({ children, ...props }) => <span {...props}>{children}</span>,
  Icon: ({ name, ...props }) => <i data-icon-name={name} {...props}></i>,
}));

const lessInclusions = {
  inclusions: [
    { name: 'inclusion1', code: 'Wi-fi', icon: 'incQfWifi', description: 'Inclusion 1' },
    { name: 'inclusion2', code: 'Wi-fi', icon: 'incQfWifi', description: 'Inclusion 2' },
    { name: 'inclusion3', code: 'Wi-fi', icon: 'incQfWifi', description: 'Inclusion 3' },
  ],
};
const moreInclusions = {
  inclusions: [...lessInclusions.inclusions, ...lessInclusions.inclusions],
};
const renderComponent = (props) => render(<Inclusions {...props} />);

describe('Inclusions', () => {
  describe('when there are 4 or less inclusions', () => {
    it('should display all inclusions', () => {
      renderComponent(lessInclusions);
      const displayedInclusions = screen.getAllByTestId('global-inclusion');

      expect(displayedInclusions).toHaveLength(3);
    });

    it('should NOT display the toggle', () => {
      renderComponent(lessInclusions);
      expect(screen.queryByRole('button', { name: /view all/i })).not.toBeInTheDocument();
    });
  });

  describe('when there are more than 4 inclusions', () => {
    it('should display only 4 inclusions and show the toggle', () => {
      renderComponent(moreInclusions);
      const displayedInclusions = screen.getAllByTestId('global-inclusion');
      const toggleButton = screen.getByRole('button', { name: /view all/i });

      expect(displayedInclusions).toHaveLength(4);
      expect(toggleButton).toBeInTheDocument();
    });

    it('should toggle inclusions when clicked', async () => {
      renderComponent(moreInclusions);
      const toggleButton = screen.getByRole('button', { name: /view all/i });

      await userEvent.click(toggleButton);

      const displayedInclusions = screen.getAllByTestId('global-inclusion');

      expect(displayedInclusions).toHaveLength(6);
      expect(screen.getByRole('button', { name: /view less/i })).toBeInTheDocument();
    });

    it('should show "View all X inclusions" when not expanded', () => {
      renderComponent(moreInclusions);
      const toggleButton = screen.getByRole('button', { name: /view all/i });
      const displayedInclusions = screen.getAllByTestId('global-inclusion');

      expect(displayedInclusions).toHaveLength(4);
      expect(toggleButton).toHaveTextContent(`View all ${moreInclusions.inclusions.length} inclusions`);
    });
  });
});

describe('when there are no inclusions', () => {
  it('should not render anything', () => {
    renderComponent([]);

    expect(screen.queryByTestId('global-inclusion')).not.toBeInTheDocument();
  });
});
