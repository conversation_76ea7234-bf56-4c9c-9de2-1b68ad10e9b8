import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import QantasPoints from './QantasPoints';
import { COMPLETE } from 'components/StepWizard';
import { getIsLoggedInFromStep } from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/sessionStorage';

jest.mock('components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/sessionStorage');
jest.mock('components/StepWizard', () => ({
  StepHeader: ({ title }) => <h2>{title}</h2>,
}));
jest.mock('./QantasPointsForm', () => (props) => <div data-testid="mock-qantas-points-form" {...props} />);
jest.mock('@qga/roo-ui/components', () => ({
  Box: (props) => <div data-testid="roo-ui-box" {...props}></div>,
}));

const updateState = jest.fn();
const defaultProps = {
  step: { updateState },
};

describe('QantasPoints', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the StepHeader with the correct title', () => {
    render(<QantasPoints {...defaultProps} />);
    expect(screen.getByText(/Earn Qantas Points/i)).toBeInTheDocument();
  });

  it('renders the QantasPointsForm', () => {
    render(<QantasPoints {...defaultProps} />);
    expect(screen.getByTestId('mock-qantas-points-form')).toBeInTheDocument();
  });

  describe('when isLoggedInFromStep', () => {
    describe('is FALSE', () => {
      beforeEach(() => {
        getIsLoggedInFromStep.mockReturnValue(false);
      });

      it('does not update the state to COMPLETE', () => {
        render(<QantasPoints {...defaultProps} />);
        expect(updateState).not.toHaveBeenCalled();
      });
    });

    describe('is TRUE', () => {
      beforeEach(() => {
        getIsLoggedInFromStep.mockReturnValue(true);
      });

      it('updates the state to COMPLETE', () => {
        render(<QantasPoints {...defaultProps} />);
        expect(updateState).toHaveBeenCalledWith(COMPLETE);
      });
    });
  });
});
