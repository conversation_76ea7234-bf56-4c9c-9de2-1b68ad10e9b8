import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useForm } from 'react-hook-form';
import useFormPersist from 'react-hook-form-persist';
import QantasPointsForm from './QantasPointsForm';
import { QFF_OR_EMPTY_REGEX, ABN_OR_EMPTY_REGEX } from 'lib/enums/checkout';
import { useLoginUrl } from 'lib/qffAuth';
import { getMemberId, getPointsClubLevel } from 'store/user/userSelectors';
import { useIsAuthenticated } from 'lib/oauth';
import { POINTS_CLUB_TIERS } from 'lib/enums/pointsClub';
import { useDataLayer } from 'hooks/useDataLayer';
import useCheckoutPointsEarned from 'components/CheckoutPage/hooks/useCheckoutPointsEarned';
import { getQuote } from 'store/quote/quoteSelectors';
import useCtaClickEvent from 'hooks/useCtaClickEvent';
import '@testing-library/jest-dom';
import { configureStore } from 'redux-mock-store';
import { Provider } from 'react-redux';
import { ThemeProvider } from 'emotion-theming';
import theme from 'lib/theme';

jest.mock('react-hook-form');
jest.mock('react-hook-form-persist');
jest.mock('components/StepWizard', () => ({
  StepForm: ({ children, handleSubmit }) => (
    <form data-testid="mock-step-form" onSubmit={handleSubmit}>
      {children}
    </form>
  ),
}));
jest.mock('store/user/userSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('lib/qffAuth');
jest.mock('hooks/useDataLayer');
jest.mock('components/CheckoutPage/hooks/useCheckoutPointsEarned');
jest.mock('store/ui/uiSelectors');
jest.mock('lib/oauth');
jest.mock('hooks/useCtaClickEvent', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    ctaClickEvent: jest.fn(),
  })),
}));

jest.mock('@qga/roo-ui/logos', () => ({
  pointsClubPlus: 'pointsClubPlus.png',
  pointsClub: 'pointsClub.png',
}));
jest.mock('@qga/roo-ui/components', () => ({
  Box: ({ children, ...props }) => (
    <div {...props} data-testId="roo-ui-box">
      {children}
    </div>
  ),
  Input: ({ name, onChange, defaultValue, type, ...props }) => (
    <input data-testid={`${name}-mock-input`} defaultValue={defaultValue} onChange={onChange} type={type} {...props} />
  ),
  Flex: ({ children, ...props }) => (
    <div {...props} data-testId="roo-ui-flex">
      {children}
    </div>
  ),
  Image: ({ alt, ...props }) => <img {...props} alt={alt} />,
  Link: ({ children, href, 'data-testid': dataTestId, ...props }) => (
    <a data-testid={dataTestId} href={href} {...props}>
      {children}
    </a>
  ),
  Icon: () => <svg data-testid="roo-ui-icon"></svg>,
  ExternalLink: ({ children, href, 'data-testid': dataTestId, ...props }) => (
    <a data-testid={dataTestId} href={href} {...props}>
      {children}
    </a>
  ),
  Text: ({ 'data-testid': dataTestId, children, ...props }) => (
    <span data-testid={dataTestId ?? 'roo-ui-text'} {...props}>
      {children}
    </span>
  ),
}));

jest.mock('components/PointsClubModal', () => () => <div>Points Club Modal</div>);
jest.mock('./ABNInformation', () => () => <div>ABN Information Component</div>);
jest.mock('./QFFInformation', () => () => <div>QFF Information Component</div>);
jest.mock('./PointsEarnSummaryText/PointsEarnSummaryText', () => () => <div>Points Earn Summary Text Component</div>);
jest.mock('@qga/components', () => ({
  FieldError: ({ error, 'data-testid': dataTestId }) => (error ? <div data-testid={dataTestId}>{error.message}</div> : null),
  FieldLabel: ({ ...props }) => <span data-testid="mock-field-label" {...props} />,
}));

const formData = {};

const completeStep = jest.fn();
const updateFormData = jest.fn();
const setValue = jest.fn();
const watch = jest.fn();
const mockHandleSubmit = jest.fn();

const mockCtaClickEvent = jest.fn();
const emitInteractionEvent = jest.fn();

const defaultProps = {
  step: {
    hasState: () => true,
    edit: () => {},
    formData,
    updateFormData,
    completeStep,
  },
};

const errors = {
  qffNumber: { message: 'qffNumber error' },
  abn: { message: 'abn error' },
};

const pointsEarned = {
  qffPoints: { total: 1234 },
  qbrPoints: { total: 234 },
};

const quote = {
  offer: {
    pointsEarned,
  },
};

let register;

const mockStore = configureStore([]);
const renderComponent = () => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <QantasPointsForm {...defaultProps} />
      </ThemeProvider>
    </Provider>,
  );
};

beforeEach(() => {
  jest.clearAllMocks();
  updateFormData.mockClear();
  emitInteractionEvent.mockClear();
  useIsAuthenticated.mockReturnValue(false);
  register = jest.fn();
  useForm.mockReturnValue({
    register,
    formState: { errors },
    handleSubmit: mockHandleSubmit,
    setValue,
    watch,
  });
  getQuote.mockReturnValue(quote);
  useCheckoutPointsEarned.mockReturnValue({
    pointsEarned,
  });
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useLoginUrl.mockReturnValue({ loginUrl: '/hotels/auth/callback?state=/foo' });
  useCtaClickEvent.mockReturnValue({ ctaClickEvent: mockCtaClickEvent });
});

it('renders the QantasPointsForm and its elements correctly', async () => {
  renderComponent();

  expect(screen.getByTestId('mock-step-form')).toBeInTheDocument();
  expect(screen.getByText('Points Earn Summary Text Component')).toBeInTheDocument();
  expect(screen.getByTestId('qffNumber-mock-input')).toBeInTheDocument();
  expect(screen.getByText('QFF Information Component')).toBeInTheDocument();
  expect(screen.getByTestId('abn-mock-input')).toBeInTheDocument();
  expect(screen.getByText('ABN Information Component')).toBeInTheDocument();
});

it('calls useCheckoutPointsEarned with the quote pointsEarned', () => {
  renderComponent();

  expect(useCheckoutPointsEarned).toHaveBeenCalledWith({ pointsEarned });
});

it('calls the useFormPersist hook with the expected params', () => {
  renderComponent();

  expect(useFormPersist).toHaveBeenCalledWith('qantasPointsForm', { setValue, watch }, { onDataRestored: expect.any(Function) });
});

describe('qffNumber field', () => {
  it('registers the expected validation rule for qffNumber', () => {
    renderComponent();
    expect(register).toHaveBeenCalledWith(
      'qffNumber',
      expect.objectContaining({
        pattern: {
          message: 'Please enter a valid Frequent Flyer Number',
          value: QFF_OR_EMPTY_REGEX,
        },
      }),
    );
  });

  it('renders the qffNumber FieldError when there is an error', () => {
    renderComponent();
    expect(screen.getByTestId('qff-number-error')).toHaveTextContent('qffNumber error');
  });
});

describe('points club logged out UI', () => {
  it('does NOT render a login button when logged in', async () => {
    useIsAuthenticated.mockReturnValue(true);
    renderComponent();

    expect(screen.queryByTestId('points-club-banner-login-cta')).not.toBeInTheDocument();
  });

  it('does render a login button when logged out', async () => {
    useIsAuthenticated.mockReturnValue(false);
    renderComponent();

    expect(screen.queryByTestId('points-club-banner-login-cta')).toBeInTheDocument();
  });

  it('dispatches an event to the data layer when the login link is clicked', async () => {
    renderComponent();
    const loginLink = screen.getByRole('link', { name: /login now/i });
    await userEvent.click(loginLink);

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Points Club Login Now Text',
      value: 'Login Now Link Selected',
    });
    expect(mockCtaClickEvent).toHaveBeenCalledWith({
      itemText: 'Login now',
      itemType: 'link',
      url: '/hotels/auth/callback?state=/foo',
    });
  });
});

describe('points club logged in UI', () => {
  const qffNumber = 'qffNumber';

  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
    getMemberId.mockReturnValue(qffNumber);
  });

  it('does not render points club specific images or text when logged out', () => {
    useIsAuthenticated.mockReturnValue(false);
    getPointsClubLevel.mockReturnValue(null);

    renderComponent();

    expect(screen.queryByAltText('Points Club Logo')).not.toBeInTheDocument();
  });

  it('renders the points club logo', () => {
    getPointsClubLevel.mockReturnValue(POINTS_CLUB_TIERS.POINTS_CLUB);
    renderComponent();

    expect(screen.getByAltText('Points Club Logo')).toHaveAttribute('src', 'pointsClub.png');
  });

  it('renders the points club plus logo', () => {
    getPointsClubLevel.mockReturnValue(POINTS_CLUB_TIERS.POINTS_CLUB_PLUS);
    renderComponent();
    expect(screen.getByAltText('Points Club Logo')).toHaveAttribute('src', 'pointsClubPlus.png');
  });

  it('renders the points club tooltip if the QFF number is changed', async () => {
    getPointsClubLevel.mockReturnValue(POINTS_CLUB_TIERS.POINTS_CLUB_PLUS);
    renderComponent();

    const qffInput = screen.getByTestId('qffNumber-mock-input');
    await userEvent.type(qffInput, '1');

    expect(screen.getByTestId('qff-points-club-notice')).toHaveTextContent(
      'Please note, only Qantas Points Club members are eligible for Points Club bonus points.',
    );
  });

  it('hides the points club tooltip if the QFF number is restored', async () => {
    getPointsClubLevel.mockReturnValue(POINTS_CLUB_TIERS.POINTS_CLUB_PLUS);
    renderComponent();

    const qffInput = screen.getByTestId('qffNumber-mock-input');
    await userEvent.type(qffInput, '1');
    expect(screen.getByTestId('qff-points-club-notice')).toBeInTheDocument();

    await userEvent.clear(qffInput);
    await userEvent.type(qffInput, qffNumber);

    expect(screen.queryByTestId('qff-points-club-notice')).not.toBeInTheDocument();
  });
});

describe('abn field', () => {
  it('registers the expected validation rule for abn', () => {
    renderComponent();
    expect(register).toHaveBeenCalledWith(
      'abn',
      expect.objectContaining({
        pattern: { message: 'Please enter a valid 11-digit ABN', value: ABN_OR_EMPTY_REGEX },
      }),
    );
  });

  it('renders the abn FieldError when there is an error', () => {
    renderComponent();

    expect(screen.getByTestId('abn-error')).toHaveTextContent('abn error');
  });
});

describe('when the user is authenticated', () => {
  const qffNumber = 'qffNumber';

  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
    getMemberId.mockReturnValue(qffNumber);
  });

  describe('when no qffNumber has been provided', () => {
    it('defaults the form data with qff login details', async () => {
      renderComponent();
      const { onDataRestored } = useFormPersist.mock.calls[0][2];

      await waitFor(() => {
        onDataRestored({ qffNumber: '' });
      });

      expect(setValue).toHaveBeenCalledWith('qffNumber', qffNumber);
    });
  });

  describe('when a qffNumber has previously been provided', () => {
    it('does not default the form data with qff login details but keeps the entered one', async () => {
      renderComponent();
      const { onDataRestored } = useFormPersist.mock.calls[0][2];

      await waitFor(() => {
        onDataRestored({ qffNumber: '1234' });
      });

      expect(setValue).toHaveBeenCalledWith('qffNumber', '1234');
    });
  });
});

describe('qffNumber population after async Redux update', () => {
  it('correctly sets qffNumber from Redux via onDataRestored when authenticated but no persisted value', async () => {
    useIsAuthenticated.mockReturnValue(true);
    getMemberId.mockReturnValue('QFF_FROM_REDUX');
    watch.mockReturnValue(null);

    renderComponent();

    const { onDataRestored } = useFormPersist.mock.calls[0][2];

    await waitFor(() => {
      onDataRestored({ qffNumber: '' });
    });

    expect(setValue).toHaveBeenCalledWith('qffNumber', 'QFF_FROM_REDUX');
  });

  it('prioritizes persisted qffNumber in onDataRestored if present', async () => {
    useIsAuthenticated.mockReturnValue(true);
    getMemberId.mockReturnValue('QFF_FROM_REDUX');
    watch.mockReturnValue(null);

    renderComponent();

    const { onDataRestored } = useFormPersist.mock.calls[0][2];

    await waitFor(() => {
      onDataRestored({ qffNumber: 'PERSISTED_QFF' });
    });

    expect(setValue).toHaveBeenCalledWith('qffNumber', 'PERSISTED_QFF');
  });
});
