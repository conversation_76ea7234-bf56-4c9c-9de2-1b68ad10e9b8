import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PointsLearnMore from './PointsLearnMore';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import { ThemeProvider } from 'emotion-theming';
import theme from 'lib/theme';
import userEvent from '@testing-library/user-event';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('lib/hooks', () => ({
  useModal: jest.fn(),
}));

jest.mock('@qga/roo-ui/components', () => ({
  Text: ({ children, 'data-testid': dataTestId }) => <div data-testid={dataTestId}>{children}</div>,
  Box: ({ children }) => <div data-testid="roo-ui-box">{children}</div>,
}));

jest.mock('components/TextButton', () => ({ children, onClick }) => (
  <button data-testid="mock-text-button" onClick={onClick}>
    {children}
  </button>
));

jest.mock('components/Modal', () => {
  return ({ children, isOpen, title, onClose }) =>
    isOpen ? (
      <div data-testid="mock-modal">
        <h2 data-testid="mock-modal-title">{title}</h2>
        <button data-testid="mock-modal-close-button" onClick={onClose}>
          Close
        </button>
        {children}
      </div>
    ) : null;
});
jest.mock('hooks/useDataLayer');

const mockStore = configureStore([]);

const renderComponent = () => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <PointsLearnMore />
      </ThemeProvider>
    </Provider>,
  );
};

const emitInteractionEvent = jest.fn();

describe('PointsLearnMore component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    useModal.mockImplementation(() => ({
      openModal: jest.fn(),
      modalProps: {
        isOpen: false,
        onClose: jest.fn(),
      },
    }));
  });

  it('renders the "Important Information" button', () => {
    renderComponent();
    expect(screen.getByRole('button', { name: /Important Information/i })).toBeInTheDocument();
  });

  it('opens the modal with the title and body text', async () => {
    const mockOpenModal = jest.fn();
    const mockCloseModal = jest.fn();
    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: {
        isOpen: false,
        onClose: mockCloseModal,
      },
    }));
    const { rerender } = renderComponent();
    const learnMoreButton = screen.getByRole('button', { name: /Important Information/i });
    await userEvent.click(learnMoreButton);

    expect(mockOpenModal).toHaveBeenCalledTimes(1);

    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: {
        isOpen: true,
        onClose: mockCloseModal,
      },
    }));

    rerender(<PointsLearnMore />);

    expect(screen.getByTestId('mock-modal-title')).toHaveTextContent('Redeeming Qantas Points for hotels');
    expect(screen.getByTestId('modal-learn-more-text')).toBeInTheDocument();
  });

  it('closes the modal when the close button is clicked', async () => {
    const mockOpenModal = jest.fn();
    const mockCloseModal = jest.fn();

    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: {
        isOpen: true,
        onClose: mockCloseModal,
      },
    }));

    const { rerender } = renderComponent();
    const closeButton = screen.getByRole('button', { name: /close/i });
    await userEvent.click(closeButton);
    expect(mockCloseModal).toHaveBeenCalledTimes(1);

    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: {
        isOpen: false,
        onClose: mockCloseModal,
      },
    }));

    rerender(<PointsLearnMore />);
    expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();
  });

  it('dispatches an event to the dataLayer', async () => {
    renderComponent();

    const learnMoreButton = screen.getByRole('button', { name: /Important Information/i });
    await userEvent.click(learnMoreButton);

    expect(emitInteractionEvent).toHaveBeenCalledTimes(1);
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Learn More Link',
      value: 'Pay In Full Selected',
    });
  });
});
