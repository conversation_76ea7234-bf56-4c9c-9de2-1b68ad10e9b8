import React from 'react';
import { useForm } from 'react-hook-form';
import useFormPersist from 'react-hook-form-persist';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import PersonalDetailsForm from './PersonalDetailsForm';
import { EMAIL_REGEX, NAME_TEXT_REGEX, PHONE_REGEX, SAFE_TEXT_REGEX, NAME_TITLES } from 'lib/enums/checkout';
import { getTitle, getFirstName, getLastName, getEmailAddress, getPhoneNumber } from 'store/user/userSelectors';
import { useIsAuthenticated } from 'lib/oauth';
import * as config from 'config';

jest.mock('react-hook-form');
jest.mock('react-hook-form-persist');
jest.mock('components/StepWizard', () => ({
  StepForm: ({ children, completeStep, handleSubmit }) => (
    <form onSubmit={handleSubmit}>
      {children}
      <button type="submit" onClick={completeStep}>
        Submit Step
      </button>
    </form>
  ),
}));
jest.mock('store/user/userSelectors');
jest.mock('lib/oauth');
jest.mock('config');
jest.mock('hooks/useDataLayer', () => ({
  useDataLayer: () => ({
    emitInteractionEvent: jest.fn(),
  }),
}));

let currentFormData = {};

const updateFormData = jest.fn((newData) => {
  currentFormData = { ...currentFormData, ...newData };
});
const completeStep = jest.fn();
const setValue = jest.fn();
const watch = jest.fn();
const reset = jest.fn();

const mockStore = configureStore([]);
let store;

const defaultProps = {
  step: { hasState: () => true, edit: () => {}, currentFormData, updateFormData, completeStep },
};

const renderComponent = (props = {}) => {
  store = mockStore({
    user: {
      title: getTitle(),
      firstName: getFirstName(),
      lastName: getLastName(),
      emailAddress: getEmailAddress(),
      phoneNumber: getPhoneNumber(),
    },
  });
  return render(
    <Provider store={store}>
      <PersonalDetailsForm {...defaultProps} step={{ ...defaultProps.step, formData: currentFormData, ...props.step }} />
    </Provider>,
  );
};

let register;

beforeEach(() => {
  currentFormData = {};
  updateFormData.mockClear();
  completeStep.mockClear();
  setValue.mockClear();
  watch.mockClear();
  reset.mockClear();

  register = jest.fn();
  useForm.mockReturnValue({
    register,
    formState: { errors: {}, isSubmitting: false },
    handleSubmit: jest.fn((cb) => (e) => {
      e.preventDefault();
      cb({});
    }),
    setValue,
    watch,
    reset,
  });
  useIsAuthenticated.mockReturnValue(false);
  config.QFF_ACCOUNT_MANAGEMENT = jest.requireActual('config').ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;

  getTitle.mockReturnValue('');
  getFirstName.mockReturnValue('');
  getLastName.mockReturnValue('');
  getEmailAddress.mockReturnValue('');
  getPhoneNumber.mockReturnValue('');
});

it('renders the StepForm and its children', () => {
  renderComponent();
  expect(screen.getByLabelText(/Title/i)).toBeInTheDocument();
  expect(screen.getByLabelText(/First name/i)).toBeInTheDocument();
  expect(screen.getByLabelText(/Last name/i)).toBeInTheDocument();
  expect(screen.getByLabelText(/Email address/i)).toBeInTheDocument();
  expect(screen.getByLabelText(/Phone number/i)).toBeInTheDocument();
  expect(screen.getByLabelText(/Special requests for your stay/i)).toBeInTheDocument();
});

it('does not default the form data based on qff login initially', () => {
  renderComponent();
  expect(updateFormData).not.toHaveBeenCalled();
});

it('calls the useFormPersist hook with the expected params', () => {
  renderComponent();
  expect(useFormPersist).toHaveBeenCalledWith('personalDetailsForm', { setValue, watch });
});

describe('title', () => {
  it('renders the title Select', () => {
    renderComponent();
    const titleSelect = screen.getByLabelText(/Title/i);
    expect(titleSelect).toBeInTheDocument();
    expect(titleSelect).toHaveValue('');
  });

  it('registers the expected validation rule', () => {
    renderComponent();
    expect(register).toHaveBeenCalledWith('title', { required: 'Please select title' });
  });

  it('renders the title FieldError when there is an error', async () => {
    useForm.mockReturnValue({
      register,
      formState: { errors: { title: { message: 'title error' } }, isSubmitting: false },
      handleSubmit: jest.fn(),
      setValue,
      watch,
      reset,
    });
    renderComponent();
    expect(screen.getByTestId('title-error')).toHaveTextContent('title error');
  });

  it('displays the correct options', () => {
    renderComponent();
    NAME_TITLES.forEach((title) => {
      expect(screen.getByRole('option', { name: title })).toBeInTheDocument();
    });
    expect(screen.getByRole('option', { name: 'Select' })).toBeInTheDocument();
  });
});

describe('firstName', () => {
  it('renders the firstName Input', () => {
    renderComponent();
    const firstNameInput = screen.getByLabelText(/First name/i);
    expect(firstNameInput).toBeInTheDocument();
    expect(firstNameInput).toHaveAttribute('id', 'firstName');
    expect(firstNameInput).toHaveAttribute('name', 'firstName');
    expect(firstNameInput).toHaveValue('');
  });

  it('registers the expected validation rule', () => {
    renderComponent();
    expect(register).toHaveBeenCalledWith('firstName', {
      required: 'Please enter your first name',
      maxLength: { message: 'First name should be less than 80 characters', value: 80 },
      pattern: {
        value: NAME_TEXT_REGEX,
        message: 'First name cannot contain numerical or non-English characters',
      },
    });
  });

  it('renders the firstName FieldError when there is an error', async () => {
    useForm.mockReturnValue({
      register,
      formState: { errors: { firstName: { message: 'firstName error' } }, isSubmitting: false },
      handleSubmit: jest.fn(),
      setValue,
      watch,
      reset,
    });
    renderComponent();
    expect(screen.getByTestId('first-name-error')).toHaveTextContent('firstName error');
  });
});

describe('lastName', () => {
  it('renders the lastName Input', () => {
    renderComponent();
    const lastNameInput = screen.getByLabelText(/Last name/i);
    expect(lastNameInput).toBeInTheDocument();
    expect(lastNameInput).toHaveAttribute('id', 'lastName');
    expect(lastNameInput).toHaveAttribute('name', 'lastName');
    expect(lastNameInput).toHaveValue('');
  });

  it('registers the expected validation rule', () => {
    renderComponent();
    expect(register).toHaveBeenCalledWith('lastName', {
      required: 'Please enter your last name',
      maxLength: { message: 'Last name should be less than 80 characters', value: 80 },
      pattern: {
        value: NAME_TEXT_REGEX,
        message: 'Last name cannot contain numerical or non-English characters',
      },
    });
  });

  it('renders the lastName FieldError when there is an error', async () => {
    useForm.mockReturnValue({
      register,
      formState: { errors: { lastName: { message: 'lastName error' } }, isSubmitting: false },
      handleSubmit: jest.fn(),
      setValue,
      watch,
      reset,
    });
    renderComponent();
    expect(screen.getByTestId('last-name-error')).toHaveTextContent('lastName error');
  });
});

describe('emailAddress', () => {
  it('renders the emailAddress Input', () => {
    renderComponent();
    const emailAddressInput = screen.getByLabelText(/Email address/i);
    expect(emailAddressInput).toBeInTheDocument();
    expect(emailAddressInput).toHaveAttribute('id', 'emailAddress');
    expect(emailAddressInput).toHaveAttribute('name', 'emailAddress');
    expect(emailAddressInput).toHaveAttribute('type', 'email');
    expect(emailAddressInput).toHaveValue('');
  });

  it('registers the expected validation rule', () => {
    renderComponent();
    expect(register).toHaveBeenCalledWith('emailAddress', {
      required: 'Please enter your email address',
      pattern: {
        value: EMAIL_REGEX,
        message: 'Please enter a valid email address',
      },
    });
  });

  it('renders the emailAddress FieldError when there is an error', async () => {
    useForm.mockReturnValue({
      register,
      formState: { errors: { emailAddress: { message: 'emailAddress error' } }, isSubmitting: false },
      handleSubmit: jest.fn(),
      setValue,
      watch,
      reset,
    });
    renderComponent();
    expect(screen.getByTestId('email-address-error')).toHaveTextContent('emailAddress error');
  });
});

describe('phoneNumber', () => {
  it('renders the phoneNumber Input', () => {
    renderComponent();
    const phoneNumberInput = screen.getByLabelText(/Phone number/i);
    expect(phoneNumberInput).toBeInTheDocument();
    expect(phoneNumberInput).toHaveAttribute('id', 'phoneNumber');
    expect(phoneNumberInput).toHaveAttribute('name', 'phoneNumber');
    expect(phoneNumberInput).toHaveValue('');
  });

  it('registers the expected validation rule', () => {
    renderComponent();
    expect(register).toHaveBeenCalledWith('phoneNumber', {
      required: 'Please enter your phone number',
      pattern: { message: 'Please enter only numbers, spaces, + or -', value: PHONE_REGEX },
    });
  });

  it('renders the phoneNumber FieldError when there is an error', async () => {
    useForm.mockReturnValue({
      register,
      formState: { errors: { phoneNumber: { message: 'phoneNumber error' } }, isSubmitting: false },
      handleSubmit: jest.fn(),
      setValue,
      watch,
      reset,
    });
    renderComponent();
    expect(screen.getByTestId('phone-number-error')).toHaveTextContent('phoneNumber error');
  });

  it('masks the phone number, rejecting anything that is not a number, a space, or +', () => {
    renderComponent();
    const phoneNumberInput = screen.getByLabelText(/Phone number/i);
    fireEvent.change(phoneNumberInput, { target: { value: '+123b 456#a' } });
    expect(setValue).toHaveBeenCalledWith('phoneNumber', '+123 456');
  });
});

describe('specialRequests', () => {
  it('renders the specialRequests TextArea', () => {
    renderComponent();
    const specialRequestsTextArea = screen.getByLabelText(/Special requests for your stay/i);
    expect(specialRequestsTextArea).toBeInTheDocument();
    expect(specialRequestsTextArea).toHaveAttribute('id', 'specialRequests');
    expect(specialRequestsTextArea).toHaveAttribute('name', 'specialRequests');
    expect(specialRequestsTextArea).toHaveValue('');
  });

  it('registers the expected validation rule', () => {
    renderComponent();
    expect(register).toHaveBeenCalledWith('specialRequests', {
      maxLength: { value: 130, message: 'Special requests should be less than 130 characters' },
      pattern: { value: SAFE_TEXT_REGEX, message: 'Special requests cannot contain special characters' },
    });
  });

  it('renders the specialRequests FieldError when there is an error', async () => {
    useForm.mockReturnValue({
      register,
      formState: { errors: { specialRequests: { message: 'specialRequests error' } }, isSubmitting: false },
      handleSubmit: jest.fn(),
      setValue,
      watch,
      reset,
    });
    renderComponent();
    expect(screen.getByTestId('special-requests-error')).toHaveTextContent('specialRequests error');
  });
});

describe('when the user is authenticated', () => {
  const mockAuthData = {
    title: 'Mr',
    firstName: 'Alan',
    lastName: 'Joyce',
    emailAddress: '<EMAIL>',
    phoneNumber: '0424123456',
  };
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
    getTitle.mockReturnValue('Mr');
    getFirstName.mockReturnValue('Alan');
    getLastName.mockReturnValue('Joyce');
    getEmailAddress.mockReturnValue('<EMAIL>');
    getPhoneNumber.mockReturnValue('0424123456');

    store = mockStore({
      user: mockAuthData,
    });

    updateFormData.mockImplementation((newData) => {
      currentFormData = { ...currentFormData, ...newData };
      Object.entries(newData).forEach(([key, value]) => {
        setValue(key, value);
      });
    });
  });

  it('form fields are initially empty when unauthenticated and populate after user logs in', async () => {
    const { rerender } = renderComponent();
    expect(screen.getByLabelText(/Title/i)).toHaveValue('');
    expect(screen.getByLabelText(/First name/i)).toHaveValue('');
    expect(screen.getByLabelText(/Last name/i)).toHaveValue('');
    expect(screen.getByLabelText(/Email address/i)).toHaveValue('');
    expect(screen.getByLabelText(/Phone number/i)).toHaveValue('');

    rerender(
      <Provider store={store}>
        <PersonalDetailsForm {...defaultProps} step={{ ...defaultProps.step, formData: currentFormData }} />
      </Provider>,
    );

    await waitFor(() => {
      expect(updateFormData).toHaveBeenCalledWith(mockAuthData);
    });

    const titleSelect = screen.getByLabelText(/Title/i);
    expect(titleSelect).toHaveValue(mockAuthData.title);

    const firstNameInput = screen.getByLabelText(/First name/i);
    expect(firstNameInput).toHaveValue(mockAuthData.firstName);

    const lastNameInput = screen.getByLabelText(/Last name/i);
    expect(lastNameInput).toHaveValue(mockAuthData.lastName);

    const emailAddressInput = screen.getByLabelText(/Email address/i);
    expect(emailAddressInput).toHaveValue(mockAuthData.emailAddress);

    const phoneNumberInput = screen.getByLabelText(/Phone number/i);
    expect(phoneNumberInput).toHaveValue(mockAuthData.phoneNumber);
  });
});

describe('when QFF_ACCOUNT_MANAGEMENT is checkout only', () => {
  beforeEach(() => {
    config.QFF_ACCOUNT_MANAGEMENT = jest.requireActual('config').ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY;
  });

  it('does NOT render the cta login prompt', () => {
    renderComponent();
    expect(screen.queryByText(/Login Prompt/i)).not.toBeInTheDocument();
  });
});
