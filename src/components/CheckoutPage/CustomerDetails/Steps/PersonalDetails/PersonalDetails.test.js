import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PersonalDetails from './PersonalDetails';
import { COMPLETE } from 'components/StepWizard';
import { getIsAuthenticated } from 'store/user/userSelectors';
import { getIsLoggedInFromStep } from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/sessionStorage';
import * as config from 'config';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';

jest.mock('components/StepWizard', () => ({
  StepHeader: ({ title }) => <div data-testid="step-header">{title}</div>,
  COMPLETE: 'complete',
}));
jest.mock('./PersonalDetailsForm', () => (props) => (
  <div data-testid="personal-details-form" {...props}>
    Personal Details Form
  </div>
));
jest.mock('store/user/userSelectors');
jest.mock('components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/sessionStorage');
jest.mock('config');

const updateState = jest.fn();

const defaultProps = {
  step: { updateState },
};

const mockStore = configureStore([]);

const renderComponent = () => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <PersonalDetails {...defaultProps} />
    </Provider>,
  );
};

beforeEach(() => {
  getIsAuthenticated.mockReturnValue(false);
  config.QFF_ACCOUNT_MANAGEMENT = jest.requireActual('config').ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;
  updateState.mockClear();
});

it('renders the StepHeader with the correct title', () => {
  renderComponent();
  expect(screen.getByTestId('step-header')).toHaveTextContent('Your details');
});

it('renders the PersonalDetailsForm', () => {
  renderComponent();

  expect(screen.getByTestId('personal-details-form')).toBeInTheDocument();
  expect(screen.getByTestId('personal-details-form')).toHaveAttribute('step');
});
describe('when isLoggedInFromStep is FALSE', () => {
  beforeEach(() => {
    getIsLoggedInFromStep.mockReturnValue(false);
  });

  it('does not update the state to COMPLETE', () => {
    renderComponent();
    expect(updateState).not.toHaveBeenCalled();
  });
});

describe('when isLoggedInFromStep is TRUE', () => {
  beforeEach(() => {
    getIsLoggedInFromStep.mockReturnValue(true);
  });

  it('updates the state to COMPLETE', () => {
    renderComponent();
    expect(updateState).toHaveBeenCalledWith(COMPLETE);
  });
});
